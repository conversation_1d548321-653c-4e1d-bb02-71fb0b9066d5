package com.pacto.adm.core.dto.solicitacaocompra;

import com.pacto.adm.core.dto.ArquivoDTO;
import com.pacto.adm.core.enumerador.SituacaoSolicitacaoCompraEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SolicitacaoCompraDTO {
    private Integer codigo;
    private String titulo;
    private Date dataSolicitacao;
    private SituacaoSolicitacaoCompraEnum situacao;
    private String descricao;
    private List<ArquivoDTO> arquivos;
    private String motivoNegacao;
}
